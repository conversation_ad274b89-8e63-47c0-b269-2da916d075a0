import express, { Express } from "express";
import dotenv from "dotenv";
import http from "http";
import cors from "cors";
import bodyParser from "body-parser";
import morgan from "morgan";
import i18n from "./helper/i18n";
import cookieParser from "cookie-parser";

dotenv.config();
const env = process.env.NEXT_NODE_ENV || "development";

import config from "../shared/config/config.json";
import dbconfig from "../shared/config/db.json";

global.config = JSON.parse(JSON.stringify(config))[env];
global.db = JSON.parse(JSON.stringify(dbconfig))[env];
import { db } from "./models/index";
import { privateRoutes, publicRoutes } from "./routes/index";
import userAuth from "./middleware/auth";
import HandleErrorMessage from "./middleware/validatorMessage";
import swaggerUi from "swagger-ui-express";
import { swaggerSpec } from "./swagger/swagger.config";
import { setupConsumers } from "./rabbitmq/consumerQueue";
import secureDocs from "./middleware/docSecurity";

db.sequelize
  .sync({ alter: true })
  .then(() => {
    console.log("re-sync db.");
  })
  .catch((error: Error) => {
    console.log("DB Error", error);
    throw error;
  });

const app: Express = express();
const router = express.Router();

app.use(morgan("combined"));

router.use(bodyParser.urlencoded({ extended: true }));
router.use(bodyParser.json());
// Enable CORS
app.use(cors("*"));
router.use(cookieParser());
router.use(i18n.init);

// /** Use routers *
router.all("/v1/private/*", userAuth);
router.use("/v1/private", privateRoutes);
router.use("/v1/public", publicRoutes);

app.use(router);

function getAllRoutes(app: any) {
  const routes: any = [];

  function formatRoute(path: string) {
    const route = path
    // eslint-disable-next-line no-useless-escape
      .replace(/^\^\\\/?\(\?\=\\\/\|\$\)\^/, "") // Remove leading regex patterns
      // eslint-disable-next-line no-useless-escape
      .replace(/\\\/\?\(\?\=\\\/\|\$\)/g, "") // Remove optional trailing slash regex
      .replace(/\^|\$|\\/g, "") // Remove remaining ^, $, and \
      .replace("(?:/([/]+?))/?", "/:id");
    return route;
  }

  function processStack(stack: any, basePath = "") {
    stack.forEach((layer: any) => {
      if (layer.route) {
        // If it's a direct route
        Object.keys(layer.route.methods).forEach((method) => {
          if (["GET", "POST", "PUT", "DELETE"].includes(method.toUpperCase())) {
            routes.push({
              method: method.toUpperCase(),
              path: formatRoute(basePath + layer.regexp.source),
              params: (layer.route.path.match(/:\w+/g) || []).map(
                (param: string) => param.replace(":", ""),
              ),
            });
          }
        });
      } else if (layer.name === "router" && layer.handle.stack) {
        // If it's a router, recurse
        processStack(layer.handle.stack, basePath + layer.regexp.source);
      }
    });
  }

  processStack(app._router.stack);
  return routes;
}

app.get("/", (req: any, res: any) => {
  const json: any = {
    message: "Welcome to Rota Microservice",
    data: getAllRoutes(app),
  };

  return res.send(json);
});

// Handle error message
router.use(HandleErrorMessage);

const server = http.createServer(app);
router.all("/uploads/*", secureDocs);
router.use("/uploads", express.static(__dirname + "/uploads"));

/** Swagger setup */
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Initialize database and RabbitMQ consumers
const initializeApp = async () => {
  try {
    /** Listen all queue from subscription-ms */
    await setupConsumers();

    server.listen(global.config.PORT, () => {
      console.log(`Server is started on`, global.config.PORT);
    });
  } catch (error) {
    console.error("Error during application initialization:", error);
    process.exit(1); // Exit the process if initialization fails
  }
};

// Start application initialization
initializeApp();

export default router;
