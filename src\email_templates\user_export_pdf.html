<!DOCTYPE html>
<html>

<head>
    <title>User Report</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
            width: 100%;
            font-size: 11px;
            color: #333333;
        }

        .report-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin: 0;
        }

        .logo-container {
            flex-shrink: 0;
        }

        .logo-container img {
            max-height: 60px;
            max-width: 150px;
            object-fit: contain;
        }

        .report-details {
            font-size: 11px;
            color: #666666;
            line-height: 1.4;
        }

        .report-details p {
            margin: 2px 0;
        }

        .table-container {
            margin-top: 20px;
        }

        .dsr-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 10px;
        }

        .dsr-table th {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
            font-weight: bold;
            color: #495057;
        }

        .dsr-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }

        .dsr-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .dsr-table tr:hover {
            background-color: #e9ecef;
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }

        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }

        @media print {
            body {
                padding: 10px;
            }

            .report-container {
                max-width: none;
            }
        }
    </style>
</head>

<body>
    <div class="report-container">
        <div class="header">
            <div class="report-title">User Report</div>
            <div class="logo-container">
                <img src="{{NAMASTE_LOGO}}">
            </div>
            <div>
                <div class="report-details" style="margin-bottom: 8px;">
                    <p style="margin: 2px 0; font-size: 11px;">Date: {{current_date}}</p>
                </div>
                <div class="report-details">
                    {{#if filters}}
                    {{#each filters}}
                    <p style="margin: 2px 0; font-size: 11px;">{{this}}</p>
                    {{/each}}
                    {{else}}
                    <p style="margin: 2px 0; font-size: 11px;">Filters Applied: None</p>
                    {{/if}}
                    {{#if GENERATED_BY_USER}}
                    <p style="margin: 2px 0; font-size: 11px;">Generated By: {{GENERATED_BY_USER}}</p>
                    {{/if}}
                </div>
            </div>
        </div>
        <div class="table-container">
            <table class="dsr-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Branch</th>
                        <th>Department</th>
                        <th>Role</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each users}}
                    <tr>
                        <td><strong>{{employment_number}}</strong></td>
                        <td>{{user_full_name}}</td>
                        <td>{{user_email}}</td>
                        <td>{{branch_name}}</td>
                        <td>{{department_name}}</td>
                        <td>{{role_name}}</td>
                        <td>
                            <span class="status-{{user_status}}">
                                {{user_status}}
                            </span>
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
</body>

</html>
