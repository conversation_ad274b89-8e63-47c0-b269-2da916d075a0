FROM node:20 AS build_image

# Set the working directory
WORKDIR /usr/src/backend-rota-ms

# Copy only package.json and package-lock.json to install dependencies
COPY package*.json ./

# Install dependencies
RUN yarn install 

# Copy the rest of the application files
COPY . .

# Build the Node.js app
RUN yarn build

# Copy the email templates into the build directory after building the app
COPY ./src/email_templates /usr/src/backend-rota-ms/build/src/email_templates

# remove dev dependencies
RUN npm prune --production

# Stage 2: Production Image
FROM node:20

# Set the working directory
WORKDIR /usr/src/backend-rota-ms

# Install production dependencies only
COPY --from=build_image /usr/src/backend-rota-ms/package.json ./package.json
COPY --from=build_image /usr/src/backend-rota-ms/node_modules ./node_modules
COPY --from=build_image /usr/src/backend-rota-ms/build ./build

# Expose the port that the Node.js app runs on
EXPOSE 9027

# Start the Node.js app
CMD ["node", "./build/src/index.js"]