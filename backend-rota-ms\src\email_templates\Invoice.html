<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
</head>

<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f9f9f9;">

    <div
        style="width: 100%; max-width: 600px; margin: 20px auto; background: #fff; border-radius: 8px; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.1); padding: 20px;">

        <!-- Invoice Header -->
        <div style="margin-bottom: 20px;">
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="font-size: 18px; padding-bottom: 10px;">Invoice {{invoice_id}}</td>
                    <td style="text-align: right;">
                        {{#if payment_status}}
                        <span
                            style="background: #d1fae5; color: #16a34a; padding: 5px 10px; border-radius: 16px; font-size: 12px;">
                            <svg style="width: 16px; height: 16px; margin-right: 4px; fill: #16a34a; vertical-align: bottom;"
                                viewBox="0 0 24 24">
                                <path
                                    d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm-1.17 15.59L7.41 14l1.41-1.41 2 2 4.59-4.59L17.41 11z" />
                            </svg>
                            Paid
                        </span>
                        {{else}}
                        <span
                            style="background: #f6d6d6; color:#c54d4d; padding: 5px 10px; border-radius: 16px; font-size: 12px;">
                            <svg style="width: 16px; height: 16px; margin-right: 4px; fill: #dc2626; vertical-align: bottom;"
                                viewBox="0 0 24 24">
                                <path
                                    d="M18.36 5.64a1 1 0 0 0-1.41 0L12 10.59 7.05 5.64a1 1 0 1 0-1.41 1.41L10.59 12l-5.95 5.95a1 1 0 1 0 1.41 1.41L12 13.41l5.95 5.95a1 1 0 1 0 1.41-1.41L13.41 12l5.95-5.95a1 1 0 0 0 0-1.41z" />
                            </svg>
                            Failed
                        </span>
                        {{/if}}
                    </td>
                </tr>
            </table>

            {{#if payment_status}}
            <div
                style="background: #f0fdf4; padding: 10px; border-bottom: 2px solid  #16a34a; border-top: 1px solid rgba(0, 0, 0, 0.1); white-space: nowrap;">
                <svg style="width: 18px; height: 18px; margin-right: 4px; fill:  #16a34a; vertical-align: bottom;"
                    viewBox="0 0 24 24">
                    <path
                        d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm-1.17 15.59L7.41 14l1.41-1.41 2 2 4.59-4.59L17.41 11z"
                        transform="scale(1) translate(2, 2)" />
                </svg>
                <p style="margin: 0; font-size: 14px; display: inline; vertical-align: middle;">Invoice Paid on
                    {{invoice_date}}
                </p>
            </div>
            {{else}}
            <div
                style="background: #f6d6d6; padding: 10px; border-bottom: 2px solid  #c54d4d; border-top: 1px solid rgba(0, 0, 0, 0.1); white-space: nowrap;">
                <svg style="width: 18px; height: 18px; margin-right: 4px; fill: #c54d4d; vertical-align: bottom;"
                    viewBox="0 0 24 24">
                    <path
                        d="M18.36 5.64a1 1 0 0 0-1.41 0L12 10.59 7.05 5.64a1 1 0 1 0-1.41 1.41L10.59 12l-5.95 5.95a1 1 0 1 0 1.41 1.41L12 13.41l5.95 5.95a1 1 0 1 0 1.41-1.41L13.41 12l5.95-5.95a1 1 0 0 0 0-1.41z" />
                </svg>
                <p style="margin: 0; font-size: 14px; display: inline; vertical-align: middle;">Invoice Failed on
                    {{invoice_date}}
                </p>
            </div>
            {{/if}}
        </div>

        <!-- Invoice Middle Section -->
        <div style="margin-bottom: 20px;">

            <table style="width: 100%; border: none;">
                <tr>
                    <td style="font-size: 14px; color: #555; text-align: left; width: 50%; padding-bottom: 5px; ">Issued
                        On</td>
                    <td style="font-size: 14px; color: #555; text-align: left; width: 50%; padding-bottom: 5px; ">Due On
                    </td>
                </tr>
                <tr>
                    <td style="font-size: 16px; text-align: left; width: 50%;">{{subscription_start_date}}</td>
                    <td style="font-size: 16px; text-align: left; width: 50%;">{{subscription_end_date}}</td>
                </tr>
            </table>

            <table style="width: 100%; border: none; margin-top: 20px; margin-bottom: 20px;">
                <tr>
                    <td style="font-size: 14px; text-align: left; color: #555; width: 50%;">From</td>
                    <td style="font-size: 14px; color: #555; width: 50%;">To</td>
                </tr>
                <tr>
                    <td style="font-size: 16px; text-align: left; width: 50%; padding-top: 5px; padding-bottom: 5px;">
                        {{from}}</td>
                    <td style="font-size: 16px; width: 50%; padding-top: 5px; padding-bottom: 5px;">{{to}}</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td style="font-size: 14px; color: #888; width: 50%; line-height: 20px;">{{from_address}}</td>
                    <td style="font-size: 14px; color: #888; width: 50%; line-height: 20px;">{{to_address}}</td>
                    <td>&nbsp;</td>
                </tr>
            </table>

            <table style="width: 100%; border: none;">
                <tr>
                    <td style="padding-bottom: 5px; font-size: 14px; color: #555;">Plan</td>
                </tr>
                <tr>
                    <td style="font-size: 16px;">{{plan_name}} - ${{plan_cost}}</td>
                </tr>
            </table>
        </div>

        <hr style="border: 1px solid #f1f1f1; margin: 20px 0;" />

        <!-- Invoice Table -->
        <div style="margin-bottom: 20px;">
            <strong style="margin: 0; font-size: 14px; color: black;">Items</strong>
            <table
                style="width: 100%; border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 8px; border-collapse: separate; border-spacing: 0; margin-top: 10px;">
                <thead>
                    <tr style="background: #f9f9f9;">
                        <th style="padding: 10px; font-size: 14px; text-align: left;">Description</th>
                        <th style="padding: 10px; font-size: 14px; text-align: left;">Amount</th>
                        <th style="padding: 10px; font-size: 14px; text-align: left;">Qty</th>
                        <th style="padding: 10px; font-size: 14px; text-align: right;">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 10px; font-size: 14px;">{{plan_name}}</td>
                        <td style="padding: 10px; font-size: 14px;">${{plan_cost}}</td>
                        <td style="padding: 10px; font-size: 14px;">1</td>
                        <td style="padding: 10px; font-size: 14px; text-align: right;">${{total_amount}}</td>
                    </tr>
                    <tr>
                        <td colspan="3" style="padding: 10px; font-size: 14px;">Total</td>
                        <td style="padding: 10px; font-size: 14px; text-align: right;">${{total_amount}}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Invoice Buttons -->
        <!-- <div style="text-align: center;">
            <button
                style="padding: 10px 20px; font-size: 14px; border: 1px solid #39596e; color: #39596e; background-color: white; border-radius: 4px; margin-right: 10px; cursor: pointer;">Download
                Receipt</button>
            <button
                style="padding: 10px 20px; font-size: 14px; border: 1px solid #39596e; color: #39596e; background-color: white; border-radius: 4px; cursor: pointer;">Mark
                as Unpaid</button>
        </div> -->

    </div>
</body>

</html>