import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface ShiftAttributes {
  id?: number;
  userId: number;
  startTime: Date;
  endTime: Date;
  status: "active" | "pending" | "deleted";
  minutesBreak: number;
  branchId: number;
  departmentId: number;
  roleId: number;
  isOpen: boolean;
  isPublished: boolean;
  isDropped?: boolean;
  isSwap: boolean;
  acknowledged?: boolean;
  createdBy?: number;
  updatedBy?: number;
  notes: string;
  organization_id?: string;
}

export enum ShiftStatus {
  active = "active",
  pending = "pending",
  deleted = "deleted",
}

export class Shift
  extends Model<ShiftAttributes, never>
  implements ShiftAttributes
{
  id!: number;
  userId!: number;
  startTime!: Date;
  endTime!: Date;
  status!: "active" | "pending" | "deleted";
  minutesBreak!: number;
  branchId!: number;
  departmentId!: number;
  roleId!: number;
  isOpen!: boolean;
  isPublished!: boolean;
  isDropped!: boolean;
  isSwap!: boolean;
  acknowledged!: boolean;
  createdBy!: number;
  updatedBy!: number;
  notes!: string;
  organization_id?: string;
}

Shift.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
    },
    startTime: {
      type: DataTypes.DATE,
    },
    endTime: {
      type: DataTypes.DATE,
    },
    status: {
      type: DataTypes.ENUM(Object.values(ShiftStatus)),
      defaultValue: ShiftStatus.pending,
    },
    minutesBreak: {
      type: DataTypes.INTEGER,
    },
    branchId: {
      type: DataTypes.INTEGER,
    },
    departmentId: {
      type: DataTypes.INTEGER,
    },
    roleId: {
      type: DataTypes.INTEGER,
    },
    isOpen: {
      type: DataTypes.BOOLEAN,
    },
    isPublished: {
      type: DataTypes.BOOLEAN,
    },
    isDropped: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isSwap: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isClaim: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    acknowledged: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
    },
    updatedBy: {
      type: DataTypes.INTEGER,
    },
    notes: {
      type: DataTypes.TEXT("long"),
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize: sequelize,
    tableName: "shifts",
    modelName: "Shifts",
  },
);
