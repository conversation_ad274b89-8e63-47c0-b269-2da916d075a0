{"development": {"use_env_variable": false, "PORT": 8027, "API_BASE_URL": "http://localhost:8027/uploads", "WEB_BASE_URL": "http://localhost:3000", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://localhost:8080/auth/", "KEYCLOAK_REALM_NAME": "orga", "KEYCLOAK_SECRET_KEY": "Tn59hpqfeR6Mg3DODmnp0wKI01ymHLUM", "KEYCLOAK_BASE_URL": "http://localhost:8080/auth/admin/realms/", "RABBITMQ_URL": "amqp://admin:jnext@123@localhost:5673", "CRON_RENEWAL_TIME": "*/10 * * * *", "JWT_SECRET_KEY": "jwt_token_secret", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "API_UPLOAD_URL": "http://localhost:9023/uploads", "JWT_EXIPIRATION_TIME": "1h"}, "staging": {"use_env_variable": false, "PORT": 9025, "WEB_BASE_URL": "https://namastevillage.theeasyaccess.com", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://keycloak-service:8080/auth/", "KEYCLOAK_REALM_NAME": "staging_orga", "KEYCLOAK_SECRET_KEY": "IVmrr3Jq3dMGFGsi4YBImbA25KGkr263", "KEYCLOAK_BASE_URL": "http://keycloak-service:8080/auth/admin/realms/", "RABBITMQ_URL": "amqp://admin:jnext@123@*************:5673", "CRON_RENEWAL_TIME": "*/10 * * * *", "JWT_SECRET_KEY": "jwt_token_staging_secret", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "API_UPLOAD_URL": "https://namastevillage.theeasyaccess.com/backend-api/v1/public/user/get-file?location=", "JWT_EXIPIRATION_TIME": "1h"}, "production": {"use_env_variable": false, "PORT": 9025, "API_BASE_URL": "https://portal.microffice.co.uk/rota-api", "WEB_BASE_URL": "https://portal.microffice.co.uk", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://************:30081/auth/", "KEYCLOAK_REALM_NAME": "micrOffice-prod", "KEYCLOAK_SECRET_KEY": "el8KAixidBsmf2e5xm4hkAMDy2AMVCPL", "KEYCLOAK_MY_SECRET_KEY": "keycklock-prod-secret-key", "KEYCLOAK_BASE_URL": "http://************:30081/auth/admin/realms/", "RABBITMQ_URL": "amqp://JNext:JnextMO2025@rabbitmq-service:5673", "CRON_RENEWAL_TIME": "*/10 * * * *", "JWT_SECRET_KEY": "jwt_token_staging_secret", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "API_UPLOAD_URL": "https://portal.microffice.co.uk/backend-api/v1/public/user/get-file?location=", "JWT_EXIPIRATION_TIME": "1h"}}