import { Request, Response } from "express";
import { Availability, AvailabilityStatus, AvailabilityType } from "../models/availability";
import { Op } from "sequelize";

export const createAvailability = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { type, userId, available, date, timeZone } = req.body;

    const availability = await Availability.create({
      date,
      userId,
      timeZone,
      type,
      available: available,
      organization_id: req.user.organization_id,
    });

    return res.status(201).json({
      status: true,
      message: res.__("AVAILABILITY_CREATED"),
      data: availability,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAvailabilities = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { startDate, endDate, userId, isAdmin }: any = req.query;

    const startDateObj = startDate ? new Date(startDate) : null;
    const endDateObj = endDate ? new Date(endDate) : null;
    const where: any = {
      status: {
        [Op.not]: AvailabilityStatus.deleted,
      },
      organization_id: req.user.organization_id
    };

    if (startDateObj && endDateObj) {
      where.date = {
        [Op.between]: [startDateObj, endDateObj],
      };
    }

    if (userId && isAdmin == "true") {
      where.userId = userId;
    } else {
      if (isAdmin !== "true") where.userId = req.user.id;
    }

    const { rows: data, count } = await Availability.findAndCountAll({
      where,
      order: [["date", "ASC"]],
      raw: true,
      nest: true,
    });

    data.map((d: Availability) => {
      d.timeZone = d?.timeZone && typeof d?.timeZone === "string" ? JSON.parse(d.timeZone) : d?.timeZone;
    })

    return res.status(200).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      count,
      data: data,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAvailabilityById = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const availability = await Availability.findOne({
      where: { 
        id: req.params.id,
        organization_id: req.user.organization_id 
      },
    });
    if (!availability) {
      return res
        .status(404)
        .json({ status: false, message: res.__("AVAILABILITY_NOT_FOUND") });
    }

    availability.timeZone = availability?.timeZone && typeof availability?.timeZone === "string" ? JSON.parse(availability?.timeZone) : availability?.timeZone;

    return res.status(200).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: availability,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const updateAvailability = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {

    const { type, available, date, timeZone } = req.body;

    const availability = await Availability.findOne({
      where: { 
        id: req.params.id,
        organization_id: req.user.organization_id 
      },
    });

    if (!availability) {
      return res
        .status(404)
        .json({ status: false, message: res.__("AVAILABILITY_NOT_FOUND") });
    }

    await Availability.update(
      { 
        date, 
        available, 
        type, 
        timeZone: type == AvailabilityType.FULL ? [] : timeZone 
      },
      { where: { id: req.params.id } },
    );

    return res.status(200).json({
      status: true,
      message: res.__("AVAILABILITY_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const deleteAvailability = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const availability = await Availability.findOne({
      where: { 
        id: req.params.id,
        organization_id: req.user.organization_id 
      },
    });
    if (!availability) {
      return res
        .status(404)
        .json({ status: false, message: res.__("AVAILABILITY_NOT_FOUND") });
    }

    await Availability.update(
      { status: AvailabilityStatus.deleted },
      { where: { id: req.params.id } },
    );

    return res
      .status(200)
      .json({ status: true, message: res.__("AVAILABILITY_DELETED") });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
