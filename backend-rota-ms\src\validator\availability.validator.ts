import { celebrate, Joi, Segments } from "celebrate";
import { AvailabilityStatus, AvailabilityType } from "../models/availability";

export const addAvailabilityValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      userId: Joi.number().required(),
      date: Joi.date().optional().allow(null, ""),
      available: Joi.boolean().required(),
      timeZone: Joi.array().optional().allow(null, ""),
      type: Joi.string()
        .valid(...Object.values(AvailabilityType))
        .required(),
      status: Joi.string()
        .valid(...Object.values(AvailabilityStatus))
        .default(AvailabilityStatus.active),
    }),
  });

export const updateAvailabilityValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      userId: Joi.number().optional(),
      date: Joi.date().optional().allow(null, ""),
      timeZone: Joi.array().optional().allow(null, ""),
      available: Joi.boolean().optional(),
      type: Joi.string()
        .valid(...Object.values(AvailabilityType))
        .optional(),
      status: Joi.string()
        .valid(...Object.values(AvailabilityStatus))
        .optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

export const deleteAvailabilityValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });
